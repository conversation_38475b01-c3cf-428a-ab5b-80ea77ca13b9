using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Models;
using BCrypt.Net;
using System.Security.Cryptography;
using System.Text;

namespace SiteStrideManager.API.Services;

public class FirmManagementService : IFirmManagementService
{
    private readonly SuperAdminDbContext _superAdminContext;
    private readonly ApplicationDbContext _applicationContext;

    public FirmManagementService(SuperAdminDbContext superAdminContext, ApplicationDbContext applicationContext)
    {
        _superAdminContext = superAdminContext;
        _applicationContext = applicationContext;
    }

    public async Task<IEnumerable<FirmDto>> GetAllFirmsAsync()
    {
        var firms = await _superAdminContext.Firms
            .OrderByDescending(f => f.CreatedAt)
            .ToListAsync();

        // Get user counts from the application database
        var firmUserCounts = new Dictionary<string, int>();
        var firmSiteCounts = new Dictionary<string, int>();

        foreach (var firm in firms)
        {
            var userCount = await _applicationContext.Users.CountAsync(u => u.FirmId == firm.Id && u.IsActive);
            var siteCount = await _applicationContext.Sites
                .Where(s => _applicationContext.Users.Any(u => u.Id == s.UserId && u.FirmId == firm.Id))
                .CountAsync();

            firmUserCounts[firm.Id] = userCount;
            firmSiteCounts[firm.Id] = siteCount;
        }

        return firms.Select(f => new FirmDto
        {
            Id = f.Id,
            Name = f.Name,
            Description = f.Description,
            Address = f.Address,
            Contact = f.Contact,
            Email = f.Email,
            LogoUrl = f.LogoUrl,
            Website = f.Website,
            TaxId = f.TaxId,
            RegistrationNumber = f.RegistrationNumber,
            IsActive = f.IsActive,
            CreatedAt = f.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            UpdatedAt = f.UpdatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            SubscriptionStatus = f.SubscriptionStatus,
            SubscriptionExpiryDate = f.SubscriptionExpiryDate?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            MaxUsers = f.MaxUsers,
            MaxSites = f.MaxSites,
            CurrentUserCount = firmUserCounts.GetValueOrDefault(f.Id, 0),
            CurrentSiteCount = firmSiteCounts.GetValueOrDefault(f.Id, 0)
        });
    }

    public async Task<FirmDto?> GetFirmByIdAsync(string firmId)
    {
        var firm = await _superAdminContext.Firms
            .FirstOrDefaultAsync(f => f.Id == firmId);

        if (firm == null)
            return null;

        // Get user and site counts from the application database
        var userCount = await _applicationContext.Users.CountAsync(u => u.FirmId == firmId && u.IsActive);
        var siteCount = await _applicationContext.Sites
            .Where(s => _applicationContext.Users.Any(u => u.Id == s.UserId && u.FirmId == firmId))
            .CountAsync();

        return new FirmDto
        {
            Id = firm.Id,
            Name = firm.Name,
            Description = firm.Description,
            Address = firm.Address,
            Contact = firm.Contact,
            Email = firm.Email,
            LogoUrl = firm.LogoUrl,
            Website = firm.Website,
            TaxId = firm.TaxId,
            RegistrationNumber = firm.RegistrationNumber,
            IsActive = firm.IsActive,
            CreatedAt = firm.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            UpdatedAt = firm.UpdatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            SubscriptionStatus = firm.SubscriptionStatus,
            SubscriptionExpiryDate = firm.SubscriptionExpiryDate?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            MaxUsers = firm.MaxUsers,
            MaxSites = firm.MaxSites,
            CurrentUserCount = userCount,
            CurrentSiteCount = siteCount
        };
    }

    public async Task<FirmDto> CreateFirmAsync(CreateFirmRequestDto request)
    {
        var firm = new SuperAdminFirm
        {
            Name = request.Name,
            Description = request.Description,
            Address = request.Address,
            Contact = request.Contact,
            Email = request.Email,
            LogoUrl = request.LogoUrl,
            Website = request.Website,
            TaxId = request.TaxId,
            RegistrationNumber = request.RegistrationNumber,
            MaxUsers = request.MaxUsers,
            MaxSites = request.MaxSites,
            SubscriptionExpiryDate = request.SubscriptionExpiryDate,
            IsActive = true
        };

        _superAdminContext.Firms.Add(firm);
        await _superAdminContext.SaveChangesAsync();

        return new FirmDto
        {
            Id = firm.Id,
            Name = firm.Name,
            Description = firm.Description,
            Address = firm.Address,
            Contact = firm.Contact,
            Email = firm.Email,
            LogoUrl = firm.LogoUrl,
            Website = firm.Website,
            TaxId = firm.TaxId,
            RegistrationNumber = firm.RegistrationNumber,
            IsActive = firm.IsActive,
            CreatedAt = firm.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            UpdatedAt = firm.UpdatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            SubscriptionStatus = firm.SubscriptionStatus,
            SubscriptionExpiryDate = firm.SubscriptionExpiryDate?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            MaxUsers = firm.MaxUsers,
            MaxSites = firm.MaxSites,
            CurrentUserCount = 0,
            CurrentSiteCount = 0
        };
    }

    public async Task<FirmDto?> UpdateFirmAsync(string firmId, UpdateFirmRequestDto request)
    {
        var firm = await _superAdminContext.Firms.FindAsync(firmId);
        if (firm == null)
            return null;

        if (!string.IsNullOrEmpty(request.Name))
            firm.Name = request.Name;

        if (request.Description != null)
            firm.Description = request.Description;

        if (!string.IsNullOrEmpty(request.Address))
            firm.Address = request.Address;

        if (!string.IsNullOrEmpty(request.Contact))
            firm.Contact = request.Contact;

        if (request.Email != null)
            firm.Email = request.Email;

        if (request.LogoUrl != null)
            firm.LogoUrl = request.LogoUrl;

        if (request.Website != null)
            firm.Website = request.Website;

        if (request.TaxId != null)
            firm.TaxId = request.TaxId;

        if (request.RegistrationNumber != null)
            firm.RegistrationNumber = request.RegistrationNumber;

        if (request.IsActive.HasValue)
            firm.IsActive = request.IsActive.Value;

        if (!string.IsNullOrEmpty(request.SubscriptionStatus))
            firm.SubscriptionStatus = request.SubscriptionStatus;

        if (request.SubscriptionExpiryDate.HasValue)
            firm.SubscriptionExpiryDate = request.SubscriptionExpiryDate.Value;

        if (request.MaxUsers.HasValue)
            firm.MaxUsers = request.MaxUsers.Value;

        if (request.MaxSites.HasValue)
            firm.MaxSites = request.MaxSites.Value;

        firm.UpdatedAt = DateTime.UtcNow;

        await _superAdminContext.SaveChangesAsync();

        return await GetFirmByIdAsync(firmId);
    }

    public async Task<bool> DeleteFirmAsync(string firmId)
    {
        var firm = await _superAdminContext.Firms.FindAsync(firmId);
        if (firm == null)
            return false;

        _superAdminContext.Firms.Remove(firm);
        await _superAdminContext.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ActivateFirmAsync(string firmId)
    {
        var firm = await _superAdminContext.Firms.FindAsync(firmId);
        if (firm == null)
            return false;

        firm.IsActive = true;
        firm.UpdatedAt = DateTime.UtcNow;

        await _superAdminContext.SaveChangesAsync();
        return true;
    }

    public async Task<bool> DeactivateFirmAsync(string firmId)
    {
        var firm = await _superAdminContext.Firms.FindAsync(firmId);
        if (firm == null)
            return false;

        firm.IsActive = false;
        firm.UpdatedAt = DateTime.UtcNow;

        await _superAdminContext.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<FirmUserDto>> GetFirmUsersAsync(string firmId)
    {
        var users = await _applicationContext.Users
            .Where(u => u.FirmId == firmId)
            .OrderByDescending(u => u.CreatedAt)
            .ToListAsync();

        return users.Select(u => new FirmUserDto
        {
            Id = u.Id,
            Email = u.Email,
            FirmName = u.FirmName,
            Address = u.Address,
            Contact = u.Contact,
            UserType = u.UserType,
            IsActive = u.IsActive,
            IsFirstLogin = u.IsFirstLogin,
            CreatedAt = u.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            LastLoginAt = u.LastLoginAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        });
    }

    public async Task<FirmCredentialsDto> CreateFirmAdminAsync(CreateFirmAdminRequestDto request)
    {
        var firm = await _superAdminContext.Firms.FindAsync(request.FirmId);
        if (firm == null)
            throw new ArgumentException("Firm not found");

        // Generate a temporary password
        var tempPassword = GenerateTemporaryPassword();

        var user = new User
        {
            Email = request.Email,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(tempPassword),
            FirmId = request.FirmId,
            FirmName = firm.Name,
            FirmLogoUrl = firm.LogoUrl,
            Address = request.Address,
            Contact = request.Contact,
            UserType = request.UserType,
            IsFirstLogin = true,
            IsActive = true
        };

        _applicationContext.Users.Add(user);
        await _applicationContext.SaveChangesAsync();

        return new FirmCredentialsDto
        {
            Email = user.Email,
            TemporaryPassword = tempPassword,
            FirmName = firm.Name,
            LoginUrl = "http://localhost:8081/login" // This should be configurable
        };
    }

    public async Task<bool> DeactivateFirmUserAsync(string userId)
    {
        var user = await _applicationContext.Users.FindAsync(userId);
        if (user == null)
            return false;

        user.IsActive = false;
        user.UpdatedAt = DateTime.UtcNow;

        await _applicationContext.SaveChangesAsync();
        return true;
    }

    public async Task<DashboardStatsDto> GetDashboardStatsAsync()
    {
        var totalFirms = await _superAdminContext.Firms.CountAsync();
        var activeFirms = await _superAdminContext.Firms.CountAsync(f => f.IsActive);
        var inactiveFirms = totalFirms - activeFirms;
        var totalUsers = await _applicationContext.Users.CountAsync();
        var totalSites = await _applicationContext.Sites.CountAsync();

        var currentMonth = DateTime.UtcNow.Month;
        var currentYear = DateTime.UtcNow.Year;

        var newFirmsThisMonth = await _superAdminContext.Firms
            .CountAsync(f => f.CreatedAt.Month == currentMonth && f.CreatedAt.Year == currentYear);

        var newUsersThisMonth = await _applicationContext.Users
            .CountAsync(u => u.CreatedAt.Month == currentMonth && u.CreatedAt.Year == currentYear);

        return new DashboardStatsDto
        {
            TotalFirms = totalFirms,
            ActiveFirms = activeFirms,
            InactiveFirms = inactiveFirms,
            TotalUsers = totalUsers,
            TotalSites = totalSites,
            TotalRevenue = 0, // This would need to be calculated based on subscriptions
            NewFirmsThisMonth = newFirmsThisMonth,
            NewUsersThisMonth = newUsersThisMonth,
            MonthlyStats = new List<MonthlyStatsDto>() // This would need more complex logic
        };
    }

    private string GenerateTemporaryPassword()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 12)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }
}
